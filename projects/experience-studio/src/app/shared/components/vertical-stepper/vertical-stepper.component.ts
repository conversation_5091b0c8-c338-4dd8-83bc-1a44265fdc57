import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
  OnDestroy,
  Output,
  EventEmitter,
  ChangeDetectorRef,
  ChangeDetectionStrategy,
  inject,
  DestroyRef,
  signal,
  computed,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MarkdownModule } from 'ngx-markdown';
import { finalize, switchMap, map, catchError, tap, filter, take } from 'rxjs/operators';
import { of, timer, Observable } from 'rxjs';
import { StepperState, StepperStateDisplayTitles } from '../../models/stepper-states.enum';
import { PollingService } from '../../services/polling.service';
import { ErrorReportingService, CodeFileForErrorReport } from '../../services/error-reporting.service';
import { NewPollingResponseProcessorService } from '../../services/new-polling-response-processor.service';
import { CodeSharingService } from '../../services/code-sharing.service';
import { SSEDataProcessorService } from '../../services/sse-data-processor.service';
import { createLogger } from '../../utils/logger';
import { ContentSanitizationService } from '../../services/content-sanitization.service';
// SSE Integration - Angular 19+ modern patterns
import { EnhancedSSEService } from '../../services/enhanced-sse.service';
import { SSEOptions } from '../../services/sse.service';
import { Subscription } from 'rxjs';
import { StepperStateService } from '../../services/stepper-state.service';
import { SSEEventData } from '../../services/sse-data-processor.service';

export interface StepperItem {
  title: string;
  description: string;
  visibleTitle: string; // Property to track the visible portion of the title for typewriter effect
  visibleDescription: string; // Property to track the visible portion of the description for typewriter effect
  completed: boolean;
  active: boolean;

  collapsed?: boolean; // Property to track collapsed state
  isTyping?: boolean; // Property to track if the description is currently being typed
  isTitleTyping?: boolean; // Property to track if the title is currently being typed
  retryCount?: number; // Property to track retry attempts for failed steps
  isRetrying?: boolean; // Property to track if the step is currently being retried (for shimmer effect)

  // ENHANCED: Progress description isolation properties
  frozenDescription?: boolean; // Property to track if the description should not be modified
  originalProgressState?: string; // Property to track the original progress state for this step
  descriptionFrozenAt?: number; // Timestamp when description was frozen

  // Timer properties
  startTime?: number; // Timestamp when the step started
  elapsedTime?: number; // Current elapsed time in seconds
  timerActive?: boolean; // Whether the timer is currently running
  completionTime?: number; // Final time when the step was completed (in seconds)
}

@Component({
  selector: 'app-vertical-stepper',
  standalone: true,
  imports: [CommonModule, MarkdownModule],
  templateUrl: './vertical-stepper.component.html',
  styleUrls: ['./vertical-stepper.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class VerticalStepperComponent implements OnChanges, OnInit, OnDestroy {
  @Input() progress: string = '';
  @Input() progressDescription: string = '';
  @Input() status: string = 'PENDING'; // PENDING, IN_PROGRESS, COMPLETED, FAILED
  @Input() theme: 'light' | 'dark' = 'light';
  @Input() restartable: boolean = false; // Whether to show restart button
  @Input() projectId: string = ''; // Project ID for API integration
  @Input() jobId: string = ''; // Job ID for API integration
  @Input() useApi: boolean = false; // Whether to use API for data or local inputs
  @Output() stepUpdated = new EventEmitter<number>(); // Emits the current step index when it changes
  @Output() retryStep = new EventEmitter<number>(); // Emits the step index when retry is clicked

  private logger = createLogger('VerticalStepperComponent');

  steps: StepperItem[] = [];
  currentStep: StepperItem | null = null;
  currentStepIndex: number = 0;
  animatingLine: boolean = false;
  private timeoutRefs: { [key: string]: any } = {};
  // private subscriptionManager = new SubscriptionManager();
  private typingSpeed: number = 3; // Ultra-fast and fluid typewriter effect for better user experience

  // Track the single user-expanded step (apart from processing step)
  private userExpandedStep: number | null = null;

  // ENHANCED: Angular 19+ Signals for reactive state management
  // Progress description isolation using signals
  private stepDescriptions = signal<Map<string, string>>(new Map());
  private frozenStepDescriptions = signal<Map<string, string>>(new Map());
  private currentProgressState = signal<string>('');

  // Computed signal for tracking step state changes
  private stepStateChanges = computed(() => {
    const currentState = this.currentProgressState();
    const descriptions = this.stepDescriptions();
    const frozenDescriptions = this.frozenStepDescriptions();

    return {
      currentState,
      totalSteps: this.steps.length,
      descriptionsCount: descriptions.size,
      frozenCount: frozenDescriptions.size
    };
  });

  // Timer-related properties
  private timerInterval: any;
  private timerUpdateInterval: number = 1000; // Update timer every second

  // Track which steps are collapsed
  private collapsedSteps: Set<number> = new Set();

  // Track animation states for blur-to-focus transitions
  private expandingSteps: Set<number> = new Set();
  private collapsingSteps: Set<number> = new Set();

  // Animation timing constant to match CSS
  private readonly ANIMATION_DURATION_MS = 500;

  // SSE Integration - Modern Angular 19+ patterns (SSE-only architecture)
  private sseConnectionSubscription: Subscription | null = null;

  // Retry mechanism state - Angular 19+ patterns
  private isRetryInProgress = signal<boolean>(false);
  private retryAttemptCount = 0;
  private maxRetryAttempts = 3;
  private lastErrorMessage: string | null = null;

  // Map of step states to their display titles
  private stepperStateMap = StepperStateDisplayTitles;

  // Expose StepperState enum to the template
  public StepperState = StepperState;

  private readonly errorReportingService = inject(ErrorReportingService);
  private readonly destroyRef = inject(DestroyRef);
  private readonly newPollingResponseProcessor = inject(NewPollingResponseProcessorService);
  private readonly codeSharingService = inject(CodeSharingService);
  private readonly sseDataProcessor = inject(SSEDataProcessorService);
  private readonly stepperStateService = inject(StepperStateService);

  constructor(
    private pollingService: PollingService,
    private cdr: ChangeDetectorRef,
    private contentSanitizationService: ContentSanitizationService,
    // SSE Integration - Angular 19+ inject pattern
    private enhancedSSEService: EnhancedSSEService
  ) {}

  ngOnInit(): void {
    // CRITICAL: Monitor regeneration state to isolate stepper during regeneration
    this.stepperStateService.regenerationActive$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(isRegenerationActive => {
        if (isRegenerationActive) {
          this.logger.info('🔒 Regeneration active - stopping stepper SSE monitoring');
          this.stopApiMonitoring();
        } else {
          this.logger.info('🔓 Regeneration inactive - stepper can resume SSE monitoring if needed');
          // Only restart if we have the required IDs and were using API
          if (this.useApi && this.projectId && this.jobId) {
            this.startApiMonitoring();
          }
        }
      });

    // Subscribe to the is_final$ observable to stop monitoring when the process is complete
    this.sseDataProcessor.is_final$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(isFinal => {
        if (isFinal) {
          this.logger.info('🏁 SSE stream marked as final. Stopping API monitoring.');
          this.stopApiMonitoring();
        }
      });

    // CRITICAL: Subscribe to progress updates to detect initial DEPLOY COMPLETED
    // Once initial generation is complete, stepper should stop listening to prevent code-regen interference
    this.sseDataProcessor.progress$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(progress => {
        if (progress === 'DEPLOY') {
          // Check if this is COMPLETED status for initial generation
          this.sseDataProcessor.status$
            .pipe(
              takeUntilDestroyed(this.destroyRef),
              filter(status => status === 'COMPLETED'),
              take(1) // Only take the first DEPLOY COMPLETED
            )
            .subscribe(() => {
              this.logger.info('🏁 Initial DEPLOY COMPLETED detected - stepper will stop listening to prevent code-regen interference');
              this.stopApiMonitoring();
            });
        }
      });

    // If using API and we have project and job IDs, start SSE monitoring
    if (this.useApi && this.projectId && this.jobId) {
      // Only start if regeneration is not active
      if (!this.stepperStateService.isRegenerationActive()) {
        this.startApiMonitoring();
      } else {
        this.logger.info('🔒 Skipping stepper SSE monitoring - regeneration is active');
      }
    }
    // Otherwise, initialize with default step if none exists and we have progress
    else if (this.steps.length === 0 && this.progress) {
      const displayTitle = this.getDisplayTitleForProgress(this.progress);
      const description = this.formatDescription(this.progressDescription) || 'Starting process...';
      this.steps.push({
        title: displayTitle,
        description: description,
        visibleTitle: '', // Start empty for typewriter effect
        visibleDescription: '', // Start empty for typewriter effect
        completed: false,
        active: true,
        isTitleTyping: true,
        isTyping: true,
        startTime: Date.now(),
        elapsedTime: 0,
        timerActive: true
      });
      this.currentStep = this.steps[0];
      this.currentStepIndex = 0;

      // Ensure processing step is always expanded
      if (this.status === 'IN_PROGRESS') {
        // Keep the processing step expanded
        this.collapsedSteps.delete(this.currentStepIndex);
      }

      // Emit the step updated event
      this.stepUpdated.emit(this.currentStepIndex);

      // Start the typewriter animation for the initial step after a short delay
      // to ensure the component is fully rendered
      setTimeout(() => {
        this.startTypewriterAnimation(this.currentStepIndex);
      }, 100);

      // Start the timer for the initial step
      this.startTimer();
    }

    // Initialize any existing steps that might have been passed in
    for (let i = 0; i < this.steps.length; i++) {
      const step = this.steps[i];
      if (!step.visibleDescription || !step.visibleTitle) {
        step.visibleTitle = step.visibleTitle || '';
        step.visibleDescription = step.visibleDescription || '';
        step.isTitleTyping = true;
        step.isTyping = true;

        // Start typewriter animation for each step with a staggered delay
        setTimeout(() => {
          this.startTypewriterAnimation(i);
        }, 100 * (i + 1));
      }
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    // ENHANCED: If using API and project or job ID changes, restart SSE monitoring with retry support
    if (this.useApi && (changes['projectId'] || changes['jobId'])) {
      if (this.projectId && this.jobId) {
        this.logger.info('🔄 Project/Job ID changed - restarting SSE monitoring with retry support');
        // Stop existing SSE monitoring if any
        this.stopApiMonitoring();
        // Start new SSE monitoring with enhanced retry support
        this.startApiMonitoringWithRetrySupport();
      }
    }

    // ENHANCED: Handle retry operations by synchronizing with existing SSE stream
    if (changes['status'] && this.status === 'IN_PROGRESS' && this.useApi) {
      // Check if this is a retry operation (status changed from FAILED to IN_PROGRESS)
      const previousStatus = changes['status'].previousValue;
      if (previousStatus === 'FAILED' || previousStatus === 'failed') {
        this.logger.info('🔄 Retry detected - synchronizing with existing SSE stream for latest progress updates');
        this.synchronizeWithExistingSSEStream();
      }
    }

    // ENHANCED: Always process status changes from parent component, even when using API
    // This ensures that FAILED states detected by the parent are properly handled
    // CRITICAL: But only if regeneration is not active
    if (changes['status'] && !this.isRegenerationActive()) {
      // If status changed to FAILED, immediately handle it regardless of API usage
      if (this.status === 'FAILED' || this.status === 'failed') {
        this.handleFailedStep(this.progress, this.progressDescription);
      }
    }

    // CRITICAL: Check regeneration state before processing any input changes
    // During regeneration, stepper should be completely isolated from updates
    const isRegenerationActive = this.isRegenerationActive();

    if (isRegenerationActive) {
      this.logger.info('🔒 Stepper ignoring input changes during regeneration:', {
        hasProgressChange: !!changes['progress'],
        hasDescriptionChange: !!changes['progressDescription'],
        hasStatusChange: !!changes['status'],
        regenerationActive: true
      });
      return; // Exit early to prevent any stepper updates during regeneration
    }

    // When not using API and progress or description changes, update the stepper
    if (!this.useApi && (changes['progress'] || changes['progressDescription'] || changes['status'])) {
      this.updateStepper();
    }
    // ENHANCED: When using API, still process progress and description changes for better synchronization
    else if (this.useApi && (changes['progress'] || changes['progressDescription'])) {
      // Only update if we have meaningful changes and we're not in an SSE-managed state
      if ((changes['progress'] && this.progress) || (changes['progressDescription'] && this.progressDescription)) {
        this.updateStepper();
      }
    }

    // Special handling for progress description changes in failed state
    // CRITICAL: Only if regeneration is not active
    if (changes['progressDescription'] && this.status === 'FAILED' && !this.isRegenerationActive()) {
      this.updateFailedStepDescription();
    }
  }

  /**
   * Updates the description of the failed step when the progress description changes
   * This ensures that the error message is always up to date
   */
  private updateFailedStepDescription(): void {
    // CRITICAL: Do not update during regeneration
    if (this.isRegenerationActive()) {
      this.logger.debug('🔒 Stepper updateFailedStepDescription() blocked during regeneration');
      return;
    }
    // Find the failed step
    const failedStepIndex = this.steps.findIndex(step => this.isFailureStep(step));
    if (failedStepIndex >= 0) {
      // Extract error message from the progress description or log field
      let errorMessage = '';

      // First try to get the error message from the progress description
      if (this.progressDescription && this.progressDescription.trim() !== '') {
        errorMessage = this.formatDescription(this.progressDescription);
      }

      // If we couldn't get an error message from the progress description,
      // try to get it from the SSE service's last status response (backward compatibility)
      if (!errorMessage && this.useApi) {
        const lastResponse = this.pollingService.getLastStatusResponse();
        if (lastResponse && lastResponse.details && lastResponse.details.log) {
          // Extract error message from the log field
          const logContent = lastResponse.details.log;
          errorMessage = this.extractErrorMessage(logContent);
        }
      }

      // If we have an error message, update the failed step description
      if (errorMessage) {
        const failedStep = this.steps[failedStepIndex];

        // Only update if the description has changed
        if (failedStep.description !== errorMessage) {
          failedStep.description = errorMessage;
          failedStep.visibleTitle = ''; // Reset visible title
          failedStep.visibleDescription = ''; // Reset visible description
          failedStep.isTitleTyping = true; // Set title typing state
          failedStep.isTyping = true; // Set typing state

          // Start typewriter animation
          this.startTypewriterAnimation(failedStepIndex);

          // Trigger change detection for OnPush
          this.cdr.markForCheck();
        }
      }
    }
  }

  ngOnDestroy(): void {
    // Clear all timeouts when component is destroyed
    this.clearAllTimeouts();
    // Stop timer
    this.stopTimer();
    // Stop SSE monitoring if active
    this.stopApiMonitoring();
    // Clear user-expanded tracking
    this.userExpandedStep = null;
  }

  /**
   * Start real-time status monitoring using Server-Sent Events (SSE)
   * Uses Angular 19+ patterns with modern SSE integration
   *
   * @description Establishes SSE connection for real-time project status updates
   * @requires projectId and jobId to be set
   */
  private startApiMonitoring(): void {
    if (!this.projectId || !this.jobId) {
      this.logger.warn('Cannot start API monitoring: missing projectId or jobId');
      return;
    }

    // Initialize SSE connection for real-time updates
    this.initializeSSEConnection();
  }

  /**
   * Initialize Server-Sent Events (SSE) connection for real-time status monitoring
   *
   * @description Establishes SSE connection with enhanced error handling and automatic reconnection
   * @uses Angular 19+ patterns: inject(), takeUntilDestroyed(), OnPush change detection
   * @features Exponential backoff, heartbeat monitoring, enhanced reliability
   */
  private initializeSSEConnection(): void {
    this.logger.info('🔌 Initializing SSE connection for stepper updates');

    // Configure SSE connection options with enhanced reliability settings and optimization
    const sseConnectionOptions: Partial<SSEOptions> = {
      reconnectInterval: 3000,
      maxReconnectAttempts: 10, // Enhanced for SSE-only architecture
      enableExponentialBackoff: true,
      backoffFactor: 1.5,
      maxBackoffInterval: 30000,
      enableHeartbeat: true,
      heartbeatInterval: 30000,
      // OPTIMIZATION: Explicitly enable since=0 parameter for initial generation
      generationType: 'initial-code-gen',
      enableSinceParameter: true
    };

    this.logger.info('🚀 Starting SSE connection from stepper component with optimization', {
      projectId: this.projectId,
      jobId: this.jobId,
      component: 'stepper',
      generationType: sseConnectionOptions.generationType,
      enableSinceParameter: sseConnectionOptions.enableSinceParameter,
      optimizationEnabled: true
    });

    // Establish SSE connection for real-time updates with initial generation context
    this.sseConnectionSubscription = this.enhancedSSEService.startMonitoring(
      this.projectId!,
      this.jobId!,
      sseConnectionOptions,
      undefined, // No fallback callback
      'initial-code-gen' // OPTIMIZATION: Specify generation type for since=0 parameter
    ).pipe(
      takeUntilDestroyed(this.destroyRef)
    ).subscribe({
      next: (data) => {
        this.logger.debug('📨 SSE data received in stepper:', data);
        // Data is automatically processed by SSE data processor
        // and exposed through reactive observables
      },
      error: (error) => {
        this.logger.error('❌ SSE connection error in stepper:', error);
        // Enhanced error handling - SSE will auto-reconnect with exponential backoff
        this.logger.info('🔄 SSE will attempt automatic reconnection with exponential backoff');
      }
    });

    // ENHANCED: Subscribe to real-time status updates via SSE with regeneration filtering
    this.enhancedSSEService.status$
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        filter(() => this.shouldProcessSSEEvents()) // CRITICAL: Filter out events during regeneration
      )
      .subscribe((status: string) => {
        this.logger.debug('📊 Stepper processing status update:', { status, isRegenerationActive: this.isRegenerationActive() });
        this.status = status;
        this.cdr.markForCheck(); // Trigger change detection for OnPush
      });

    // ENHANCED: Subscribe to real-time progress updates via SSE with regeneration filtering
    this.enhancedSSEService.progress$
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        filter(() => this.shouldProcessSSEEvents()) // CRITICAL: Filter out events during regeneration
      )
      .subscribe((progress: string) => {
        if (progress) {
          this.logger.debug('📊 Stepper processing progress update:', { progress, isRegenerationActive: this.isRegenerationActive() });
          this.progress = progress;
          this.updateStepper();
          this.cdr.markForCheck(); // Trigger change detection for OnPush
        }
      });

    // ENHANCED: Subscribe to real-time progress description updates via SSE with regeneration filtering
    this.enhancedSSEService.progressDescription$
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        filter(() => this.shouldProcessSSEEvents()) // CRITICAL: Filter out events during regeneration
      )
      .subscribe((description: string) => {
        if (description) {
          this.logger.debug('📊 Stepper processing description update:', { description: description.substring(0, 100), isRegenerationActive: this.isRegenerationActive() });
          this.progressDescription = description;
          this.updateStepper();
          this.cdr.markForCheck(); // Trigger change detection for OnPush
        }
      });

    this.logger.info('✅ SSE connection initialized successfully for stepper');
  }

  /**
   * ENHANCED: Start API monitoring with retry support
   * Ensures stepper receives latest progress updates during retry operations
   */
  private startApiMonitoringWithRetrySupport(): void {
    this.logger.info('🔄 Starting API monitoring with enhanced retry support');

    if (!this.projectId || !this.jobId) {
      this.logger.warn('⚠️ Cannot start API monitoring - missing project or job ID');
      return;
    }

    // Use the standard startApiMonitoring but with enhanced logging
    this.startApiMonitoring();

    this.logger.info('✅ API monitoring with retry support started successfully');
  }

  /**
   * ENHANCED: Synchronize with existing SSE stream during retry operations
   * Does NOT create new connections - just resyncs with current stream
   */
  private synchronizeWithExistingSSEStream(): void {
    this.logger.info('🔄 Synchronizing stepper with existing SSE stream for retry operation');

    if (!this.projectId || !this.jobId) {
      this.logger.warn('⚠️ Cannot synchronize with SSE stream - missing project or job ID');
      return;
    }

    // CRITICAL: Do NOT stop/restart SSE connection - just resync with existing stream
    // The SSE connection should already be active and receiving events
    this.logger.info('🔄 Resyncing stepper state with ongoing SSE stream data');

    // Force update stepper state from current SSE observables
    this.resyncStepperStateFromSSE();

    this.logger.info('✅ Stepper synchronized with existing SSE stream');
  }

  /**
   * ENHANCED: Resync stepper state from current SSE observables
   * Ensures stepper displays latest progress from ongoing SSE stream
   */
  private resyncStepperStateFromSSE(): void {
    this.logger.info('🔄 Resyncing stepper state from current SSE observables');

    // Subscribe to get latest values from SSE observables and update stepper
    // Use take(1) to get current values without ongoing subscription
    this.enhancedSSEService.status$.pipe(
      takeUntilDestroyed(this.destroyRef),
      take(1)
    ).subscribe(currentStatus => {
      if (currentStatus) {
        this.status = currentStatus;
        this.logger.debug('🔄 Updated status from SSE:', currentStatus);
      }
    });

    this.enhancedSSEService.progress$.pipe(
      takeUntilDestroyed(this.destroyRef),
      take(1)
    ).subscribe(currentProgress => {
      if (currentProgress) {
        this.progress = currentProgress;
        this.logger.debug('🔄 Updated progress from SSE:', currentProgress);
      }
    });

    this.enhancedSSEService.progressDescription$.pipe(
      takeUntilDestroyed(this.destroyRef),
      take(1)
    ).subscribe(currentDescription => {
      if (currentDescription) {
        this.progressDescription = currentDescription;
        this.logger.debug('🔄 Updated description from SSE:', currentDescription.substring(0, 100));
      }
    });

    // Update stepper display with latest data
    this.updateStepper();
    this.cdr.markForCheck();

    this.logger.info('✅ Stepper state resync initiated from SSE observables');
  }

  /**
   * ENHANCED: Check if stepper should process SSE events
   * Filters out 'code-regen' events and events during active regeneration
   */
  private shouldProcessSSEEvents(): boolean {
    // Check if regeneration is currently active
    const isRegenerationActive = this.isRegenerationActive();

    if (isRegenerationActive) {
      this.logger.debug('🚫 Stepper ignoring SSE events - regeneration is active');
      return false;
    }

    // Additional check: Only process 'initial-code-gen' events
    // This is handled by the SSE service configuration, but we add extra safety here
    return true;
  }

  /**
   * ENHANCED: Check if regeneration is currently active
   * Uses stepper state service to detect regeneration state
   */
  private isRegenerationActive(): boolean {
    try {
      return this.stepperStateService.isRegenerationActive();
    } catch (error) {
      this.logger.warn('⚠️ Could not check regeneration state, defaulting to false:', error);
      return false;
    }
  }



  /**
   * Stop real-time status monitoring and clean up SSE connection
   *
   * @description Properly closes SSE connection and cleans up subscriptions
   * @uses Angular 19+ patterns with proper resource cleanup
   * @ensures No memory leaks from unclosed connections or subscriptions
   */
  private stopApiMonitoring(): void {
    if (this.enhancedSSEService.isMonitoring()) {
      this.logger.info('🧹 Stopping SSE connection in stepper');
      this.enhancedSSEService.stopMonitoring();
    }

    if (this.sseConnectionSubscription) {
      this.sseConnectionSubscription.unsubscribe();
      this.sseConnectionSubscription = null;
    }

    // Clean up any remaining polling service state (for backward compatibility)
    this.pollingService.stopPolling();
  }

  // Clear all timeouts
  private clearAllTimeouts(): void {
    Object.values(this.timeoutRefs).forEach(timeoutId => clearTimeout(timeoutId));
    this.timeoutRefs = {};
  }

  /**
   * Clean up user-expanded tracking for invalid step indices
   */
  private cleanupUserExpandedTracking(): void {
    // Remove tracked index if it's beyond the current steps length
    if (this.userExpandedStep !== null && this.userExpandedStep >= this.steps.length) {
      this.userExpandedStep = null;
    }
  }

  /**
   * Format description to ensure proper markdown and extract error messages from JSON if needed
   * @param description The description to format
   * @returns The formatted description
   */
  private formatDescription(description?: string): string {
    if (!description) return '';

    // Use the content sanitization service to preprocess the description
    // This will handle <mlo_artifact> tags, JSON error messages, and HTML sanitization
    return this.contentSanitizationService.preprocessStepperDescription(description);
  }

  // Format title to replace underscores with spaces
  formatTitle(title: string): string {
    return title.replace(/_/g, ' ');
  }

  /**
   * Gets sanitized description for safe markdown rendering
   * @param description The description to sanitize
   * @returns Sanitized description safe for markdown rendering
   */
  getSanitizedDescription(description: string): string {
    return this.contentSanitizationService.sanitizeForMarkdown(description);
  }

  // Get the status of a step
  getStepStatus(index: number): string {
    if (index < this.currentStepIndex) return 'completed';
    if (index === this.currentStepIndex) return 'active';
    // Removed next step display - only show completed and active steps
    return 'future';
  }

  // Check if a step should be shown
  shouldShowStep(index: number): boolean {
    // Only show current step and all completed steps
    // Completely hide all future steps including next step
    return index <= this.currentStepIndex;
  }

  // Check if a step's line should be hidden
  shouldHideStepLine(index: number): boolean {
    // Hide line if the next step (index + 1) is not shown yet
    // This prevents the progress line from extending to future steps
    return (index + 1) > this.currentStepIndex;
  }

  // Check if a step's line should be animating
  isLineAnimating(index: number): boolean {
    return this.animatingLine && index === this.currentStepIndex - 1;
  }

  // Check if a step is collapsed
  isStepCollapsed(index: number): boolean {
    return this.collapsedSteps.has(index);
  }

  // Check if a step is expanding
  isStepExpanding(index: number): boolean {
    return this.expandingSteps.has(index);
  }

  // Check if a step is collapsing
  isStepCollapsing(index: number): boolean {
    return this.collapsingSteps.has(index);
  }

  // Check if a step is expanded (not collapsed and not animating)
  isStepExpanded(index: number): boolean {
    return !this.collapsedSteps.has(index) && !this.expandingSteps.has(index) && !this.collapsingSteps.has(index);
  }

  // Check if a step can be collapsed (processing steps cannot be collapsed)
  canStepBeCollapsed(index: number): boolean {
    const isProcessingStep = this.status === 'IN_PROGRESS' && index === this.currentStepIndex;
    return !isProcessingStep;
  }

  /**
   * Check if we're at the final deployment stage
   * Only stop stepper when we reach DEPLOYED/DEPLOY progress state
   */
  private isFinalDeploymentStage(): boolean {
    const progressUpper = this.progress.toUpperCase();

    const isFinalState = (
      progressUpper === 'DEPLOYED' ||
      progressUpper === 'DEPLOY'
    );

    // this.logger.info(`🔍 Checking if at final deployment stage:`, {
    //   progress: this.progress,
    //   status: this.status,
    //   isFinalState: isFinalState
    // });

    return isFinalState;
  }

  // Toggle the collapsed state of a step
  toggleStepCollapse(index: number): void {
    const isCurrentlyCollapsed = this.collapsedSteps.has(index);
    const isProcessingStep = this.status === 'IN_PROGRESS' && index === this.currentStepIndex;

    // FEATURE 1: Processing step should not be collapsible
    if (isProcessingStep) {
      // Do not allow collapsing the processing step
      return;
    }

    // Prevent toggling if already animating
    if (this.expandingSteps.has(index) || this.collapsingSteps.has(index)) {
      return;
    }

    if (isCurrentlyCollapsed) {
      // User is expanding a step
      this.startExpandAnimation(index);

      // Smart collapse: Collapse all other steps except processing step and the one being expanded
      for (let i = 0; i < this.steps.length; i++) {
        if (i !== index && !(this.status === 'IN_PROGRESS' && i === this.currentStepIndex)) {
          if (!this.collapsedSteps.has(i)) {
            this.startCollapseAnimation(i);
          }
        }
      }

      // Track this as user-expanded (unless it's the processing step)
      if (!isProcessingStep) {
        this.userExpandedStep = index;
      }

      // If the step is being expanded and hasn't completed its typewriter animation,
      // restart the animation if it was interrupted
      const step = this.steps[index];
      if (step && step.description && (!step.visibleDescription || step.visibleDescription.length < step.description.length)) {
        step.isTyping = true;
        this.startTypewriterAnimation(index);
      }
    } else {
      // User is collapsing a step
      this.startCollapseAnimation(index);

      // Remove from user-expanded tracking
      if (this.userExpandedStep === index) {
        this.userExpandedStep = null;
      }
    }

    // Trigger change detection for OnPush
    this.cdr.markForCheck();
  }

  // Start expand animation with blur-to-focus effect
  private startExpandAnimation(index: number): void {
    // Clear any existing animation states
    this.collapsingSteps.delete(index);
    this.expandingSteps.add(index);

    // Remove from collapsed immediately to start expansion
    this.collapsedSteps.delete(index);

    // Set timeout to complete the animation
    setTimeout(() => {
      this.expandingSteps.delete(index);
      this.cdr.markForCheck();
    }, this.ANIMATION_DURATION_MS);

    this.cdr.markForCheck();
  }

  // Start collapse animation with sharp-to-blur effect
  private startCollapseAnimation(index: number): void {
    // Clear any existing animation states
    this.expandingSteps.delete(index);
    this.collapsingSteps.add(index);

    // Set timeout to complete the animation
    setTimeout(() => {
      this.collapsingSteps.delete(index);
      this.collapsedSteps.add(index);
      this.cdr.markForCheck();
    }, this.ANIMATION_DURATION_MS);

    this.cdr.markForCheck();
  }

  // Restart the stepper
  restartStepper(): void {
    this.clearAllTimeouts();
    this.steps = [];
    this.currentStepIndex = 0;
    this.currentStep = null;
    this.animatingLine = false;
    this.collapsedSteps.clear();
    this.expandingSteps.clear();
    this.collapsingSteps.clear();

    // If using API, restart SSE monitoring
    if (this.useApi && this.projectId && this.jobId) {
      this.stopApiMonitoring();
      this.startApiMonitoring();
    }
    // Otherwise, if we have a progress value, initialize with it
    else if (this.progress) {
      const displayTitle = this.getDisplayTitleForProgress(this.progress);
      const description = this.formatDescription(this.progressDescription) || 'Starting process...';
      this.steps.push({
        title: displayTitle,
        description: description,
        visibleTitle: '', // Start empty for typewriter effect
        visibleDescription: '', // Start empty for typewriter effect
        completed: false,
        active: true,
        isTitleTyping: true,
        isTyping: true,
        startTime: Date.now(),
        elapsedTime: 0,
        timerActive: true
      });
      this.currentStep = this.steps[0];

      // Ensure only the first step is expanded
      // First, collapse all steps except the processing step
      const processingStepIndex = (this.status === 'IN_PROGRESS' || this.status === 'in-progress') ? 0 : -1;
      for (let i = 0; i < this.steps.length; i++) {
        // Don't collapse the processing step
        if (i !== processingStepIndex) {
          this.collapsedSteps.add(i);
        }
      }
      // Then expand only the first step
      this.collapsedSteps.delete(0);

      // Emit the step updated event
      this.stepUpdated.emit(0);

      // Start the typewriter animation for the restarted step
      this.startTypewriterAnimation(0);

      // Start the timer for the restarted step
      this.startTimer();
    }

    // Trigger change detection for OnPush
    this.cdr.markForCheck();
  }

  /**
   * Completely resets the stepper state
   * This is a public method that can be called from parent components
   * to reset the stepper when starting a new project
   */
  public resetStepper(): void {
    // Clear all timeouts to prevent any pending animations
    this.clearAllTimeouts();

    // Reset all state variables
    this.steps = [];
    this.currentStepIndex = 0;
    this.currentStep = null;
    this.animatingLine = false;
    this.collapsedSteps.clear();
    this.expandingSteps.clear();
    this.collapsingSteps.clear();

    // Reset input properties
    this.progress = '';
    this.progressDescription = '';
    this.status = 'PENDING';

    // Stop any API SSE monitoring
    if (this.useApi) {
      this.stopApiMonitoring();
    }

    // Emit an event to notify parent components
    this.stepUpdated.emit(-1); // -1 indicates a complete reset

    // Trigger change detection for OnPush
    this.cdr.markForCheck();
  }

  private updateStepper(): void {
    // CRITICAL: Additional safety check - do not update stepper during regeneration
    if (this.isRegenerationActive()) {
      this.logger.debug('🔒 Stepper updateStepper() blocked during regeneration');
      return;
    }

    // If we have a new progress state, add it as a step if it doesn't exist
    if (this.progress && this.progress.trim() !== '') {
      // Get the display title for this progress state
      const displayTitle = this.getDisplayTitleForProgress(this.progress);

      // Check if we already have this step
      const existingStepIndex = this.steps.findIndex(
        step => step.title === displayTitle || step.title === this.progress
      );

      // Special case: If progress is BUILD_FAILED, handle it through the failure logic
      // regardless of the status (could be FAILED or IN_PROGRESS)
      if (this.progress === StepperState.BUILD_FAILED) {
        this.handleFailedStep(this.progress, this.progressDescription);
        return;
      }

      // If we're in a FAILED state, handle it through the dedicated failure logic
      if (this.status === 'FAILED' || this.status === 'failed') {
        // Always call handleFailedStep when in FAILED state to ensure the error message is updated
        this.handleFailedStep(this.progress, this.progressDescription);
        return;
      }

      if (existingStepIndex === -1) {
        // Add new step
        const description = this.formatDescription(this.progressDescription) || '';
        const newStep: StepperItem = {
          title: displayTitle,
          description: description,
          visibleTitle: '', // Start empty for typewriter effect
          visibleDescription: '', // Start empty for typewriter effect
          completed: false,
          active: true,
          isTitleTyping: true,
          isTyping: true,
          startTime: Date.now(),
          elapsedTime: 0,
          timerActive: true
        };

        // Mark previous step as completed and apply smart collapse
        if (this.steps.length > 0) {
          const prevStepIndex = this.steps.length - 1;
          this.steps[prevStepIndex].completed = true;
          this.steps[prevStepIndex].active = false;
          // Stop the timer for the previous step
          this.stopStepTimer(prevStepIndex);

          // Smart collapse: Collapse the previous step when it completes,
          // but only if it's not user-expanded
          if (this.userExpandedStep !== prevStepIndex) {
            this.collapsedSteps.add(prevStepIndex);
          }

          // Smart collapse: Collapse other steps that are not user-expanded
          // and not the new processing step
          for (let i = 0; i < this.steps.length; i++) {
            if (i !== prevStepIndex && this.userExpandedStep !== i) {
              this.collapsedSteps.add(i);
            }
          }
        }

        // Start line animation
        this.animatingLine = true;
        this.timeoutRefs['line-animation'] = setTimeout(() => {
          this.animatingLine = false;
        }, 2500);

        this.steps.push(newStep);
        this.currentStep = newStep;
        this.currentStepIndex = this.steps.length - 1;

        // Ensure the processing step is expanded
        this.collapsedSteps.delete(this.currentStepIndex);

        // Emit the step updated event
        this.stepUpdated.emit(this.currentStepIndex);

        // Start the typewriter animation for the new step
        this.startTypewriterAnimation(this.currentStepIndex);

        // Start the timer if it's not already running
        if (!this.timerInterval) {
          this.startTimer();
        }

        // Ensure smooth scrolling to the new step
        setTimeout(() => {
          const stepElements = document.querySelectorAll('.stepper-item');
          if (stepElements && stepElements.length > 0) {
            const lastStep = stepElements[stepElements.length - 1] as HTMLElement;
            if (lastStep) {
              lastStep.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
            }
          }
        }, 100);
      } else {
        // ENHANCED: Progress description isolation - prevent overwriting completed step descriptions
        const existingStep = this.steps[existingStepIndex];

        // Check if this step's description should be frozen (completed step)
        const shouldFreezeDescription = existingStep.completed || existingStep.frozenDescription;

        // Only update description if step is not completed and not frozen
        if (!shouldFreezeDescription && this.progressDescription && this.progressDescription.trim() !== '') {
          const newDescription = this.formatDescription(this.progressDescription);

          // Only update if the description has actually changed and this is the current active step
          if (existingStep.description !== newDescription && existingStep.active) {
            existingStep.description = newDescription;
            existingStep.visibleTitle = ''; // Reset visible title
            existingStep.visibleDescription = ''; // Reset visible description
            existingStep.isTitleTyping = true; // Set title typing state
            existingStep.isTyping = true; // Set typing state
            this.startTypewriterAnimation(existingStepIndex); // Start typewriter animation

            // Store the description in our signal-based state management
            this.updateStepDescription(this.progress, newDescription);
          }
        } else if (shouldFreezeDescription) {
          this.logger.info('🔒 Preserving frozen description for completed step:', {
            stepIndex: existingStepIndex,
            stepTitle: existingStep.title,
            isCompleted: existingStep.completed,
            isFrozen: existingStep.frozenDescription,
            progressState: this.progress,
            preservedDescription: existingStep.description.substring(0, 50) + '...'
          });
        }

        // Update the title to make sure it uses the display title
        this.steps[existingStepIndex].title = displayTitle;

        // ENHANCED: Store original progress state for this step if not already set
        if (!existingStep.originalProgressState) {
          existingStep.originalProgressState = this.progress;
        }

        this.currentStep = this.steps[existingStepIndex];
        this.currentStepIndex = existingStepIndex;

        // Make sure this step is active and update step states
        for (let i = 0; i < this.steps.length; i++) {
          const wasCompleted = this.steps[i].completed;
          this.steps[i].active = i === existingStepIndex;
          this.steps[i].completed = i < existingStepIndex;

          // ENHANCED: Freeze description when step becomes completed
          if (!wasCompleted && this.steps[i].completed) {
            this.freezeStepDescription(i);
          }
        }

        // Smart collapse: Only collapse steps that are not user-expanded
        // and not the current processing step
        for (let i = 0; i < this.steps.length; i++) {
          if (i !== existingStepIndex && this.userExpandedStep !== i) {
            this.collapsedSteps.add(i);
          }
        }

        // Ensure the processing step is expanded
        this.collapsedSteps.delete(existingStepIndex);
      }
    }

    // Handle special status cases
    // Only treat as final completion when we're at deployment stage
    if ((this.status === 'COMPLETED' || this.status === 'completed') && this.isFinalDeploymentStage()) {
      // this.logger.info('🎉 Final deployment stage completed - marking all steps as completed');

      // If status is COMPLETED and we're at final deployment stage, mark all steps as completed and stop their timers
      this.steps.forEach((step, index) => {
        step.completed = true;
        step.active = false;

        // ENHANCED: Freeze description for all completed steps
        this.freezeStepDescription(index);

        // Stop timer and capture completion time for any active timers
        if (step.timerActive && step.startTime) {
          step.completionTime = Math.floor((Date.now() - step.startTime) / 1000);
          step.elapsedTime = step.completionTime;
          step.timerActive = false;
        }
      });
    } else if ((this.status === 'COMPLETED' || this.status === 'completed') && !this.isFinalDeploymentStage()) {
      // this.logger.info('⏳ Step completed but not at final deployment stage - continuing stepper');

      // Mark current step as completed but don't stop the entire stepper
      if (this.currentStep) {
        this.currentStep.completed = true;
        this.currentStep.active = false;

        // ENHANCED: Freeze description when step is completed
        const currentStepIndex = this.steps.indexOf(this.currentStep);
        if (currentStepIndex !== -1) {
          this.freezeStepDescription(currentStepIndex);
        }

        // Stop timer for current step
        if (this.currentStep.timerActive && this.currentStep.startTime) {
          this.currentStep.completionTime = Math.floor((Date.now() - this.currentStep.startTime) / 1000);
          this.currentStep.elapsedTime = this.currentStep.completionTime;
          this.currentStep.timerActive = false;
        }
      }
    }

    // Only proceed with final completion logic if we're at the final deployment stage
    if ((this.status === 'COMPLETED' || this.status === 'completed') && this.isFinalDeploymentStage()) {

      // Check if we have a BUILD_SUCCEEDED progress
      if (this.progress === StepperState.BUILD_SUCCEEDED) {
        // For BUILD_SUCCEEDED, we want to show "Build Completed" instead of "Completed"
        const buildSucceededStepIndex = this.steps.findIndex(
          step => step.title === this.getDisplayTitleForProgress(StepperState.BUILD_SUCCEEDED)
        );

        if (buildSucceededStepIndex === -1 && this.steps.length > 0) {
          const description = this.formatDescription(this.progressDescription) || 'Build completed successfully.';
          const buildSucceededStep: StepperItem = {
            title: this.getDisplayTitleForProgress(StepperState.BUILD_SUCCEEDED),
            description: description,
            visibleTitle: '', // Start empty for typewriter effect
            visibleDescription: '', // Start empty for typewriter effect
            completed: true, // Mark as completed to show checkmark
            active: true, // Make it active
            isTitleTyping: true,
            isTyping: true,
            startTime: Date.now(),
            elapsedTime: 0,
            timerActive: false // Completed steps don't need active timers
          };

          // Add the BUILD_SUCCEEDED step
          this.addFinalStep(buildSucceededStep);
          return; // Exit early to avoid adding the generic COMPLETED step
        } else if (buildSucceededStepIndex !== -1) {
          // Update the existing BUILD_SUCCEEDED step
          const step = this.steps[buildSucceededStepIndex];
          step.completed = true;
          step.active = true;

          // Update description if needed
          if (this.progressDescription && this.progressDescription.trim() !== '') {
            const newDescription = this.formatDescription(this.progressDescription);
            if (step.description !== newDescription) {
              step.description = newDescription;
              step.visibleTitle = ''; // Reset visible title
              step.visibleDescription = ''; // Reset visible description
              step.isTitleTyping = true; // Set title typing state
              step.isTyping = true; // Set typing state
              this.startTypewriterAnimation(buildSucceededStepIndex);
            }
          }

          // Make this the current step
          this.currentStep = step;
          this.currentStepIndex = buildSucceededStepIndex;

          // Smart collapse: Only collapse steps that are not user-expanded
          for (let i = 0; i < this.steps.length; i++) {
            if (i !== buildSucceededStepIndex && this.userExpandedStep !== i) {
              this.collapsedSteps.add(i);
            }
          }

          // Ensure this step is expanded
          this.collapsedSteps.delete(buildSucceededStepIndex);

          this.stepUpdated.emit(buildSucceededStepIndex);
          return; // Exit early
        }
      }


    } else if (this.status === 'FAILED' || this.status === 'failed') {
      // If status is FAILED, handle the failure appropriately
      this.handleFailedStep(this.progress, this.progressDescription);
    }

    // Clean up user-expanded tracking for any invalid indices
    this.cleanupUserExpandedTracking();
  }

  /**
   * Handles a failed step, creating or updating it as needed
   * @param progress The progress state that failed
   * @param progressDescription The description of the failure
   */
  private handleFailedStep(progress: string, progressDescription: string): void {
    // CRITICAL: Do not handle failed steps during regeneration
    if (this.isRegenerationActive()) {
      this.logger.debug('🔒 Stepper handleFailedStep() blocked during regeneration');
      return;
    }

    // First, determine the appropriate failure step title based on the current progress
    let failedStepTitle = '';
    let defaultFailureMessage = 'Process failed.';

    // If it's a build failure or already BUILD_FAILED, use the BUILD_FAILED state
    if (progress === StepperState.BUILD_STARTED || progress === StepperState.BUILD_FAILED) {
      failedStepTitle = this.getDisplayTitleForProgress(StepperState.BUILD_FAILED);
      defaultFailureMessage = 'Build process failed.';
    } else {
      // For other steps, create a custom failure title based on the current step
      // First get the current step's display title
      const currentStepTitle = this.getDisplayTitleForProgress(progress);
      failedStepTitle = `${currentStepTitle} Failed`;
    }

    // For BUILD_FAILED progress, we always want to create a new step
    // This ensures we show "Build Failed" as a separate step with an X icon
    let failedStepIndex = -1;

    // Only look for existing failure steps if it's not BUILD_FAILED progress
    if (progress !== StepperState.BUILD_FAILED) {
      // Check if we already have a failure step
      failedStepIndex = this.steps.findIndex(
        step => step.title === failedStepTitle || this.isFailureStep(step)
      );
    }

    // Set the status to FAILED to ensure the retry button is displayed
    this.status = 'FAILED';

    // Extract error message from the log field if available
    let errorMessage = '';

    // First try to get the error message from the progress description
    if (progressDescription && progressDescription.trim() !== '') {
      errorMessage = this.formatDescription(progressDescription);
    }

    // If we couldn't get an error message from the progress description,
    // try to get it from the service's last status response (backward compatibility)
    if (!errorMessage && this.useApi) {
      const lastResponse = this.pollingService.getLastStatusResponse();
      if (lastResponse && lastResponse.details && lastResponse.details.log) {
        // Extract error message from the log field
        const logContent = lastResponse.details.log;
        errorMessage = this.extractErrorMessage(logContent);
      }
    }

    // If we still don't have an error message, use the default
    const formattedDescription = errorMessage || defaultFailureMessage;

    if (failedStepIndex === -1) {
      // We don't have a failure step yet, so create one
      const failedStep: StepperItem = {
        title: failedStepTitle,
        description: formattedDescription, // Use the formatted description
        visibleTitle: '', // Start empty for typewriter effect
        visibleDescription: '', // Start empty for typewriter effect
        completed: false,
        active: true,
        isTitleTyping: true,
        isTyping: true,
        retryCount: 0, // Initialize retry count for failed steps
        startTime: Date.now(),
        elapsedTime: 0,
        timerActive: false // Failed steps don't need active timers
      };

      // If we have a current step, mark it appropriately
      if (this.currentStep) {
        this.currentStep.active = false;
        // Ensure the step is not marked as completed to prevent showing checkmark
        this.currentStep.completed = false;

        // Also ensure that all previous steps are properly marked
        for (let i = 0; i < this.steps.length; i++) {
          // If this is the current step or a step that was in progress when failure occurred,
          // mark it as not completed
          if (i === this.currentStepIndex ||
              (this.steps[i].active && !this.steps[i].completed)) {
            this.steps[i].completed = false;
            this.steps[i].active = false;
          }
        }
      }

      // Smart collapse: Collapse steps that are not user-expanded
      for (let i = 0; i < this.steps.length; i++) {
        if (this.userExpandedStep !== i) {
          this.collapsedSteps.add(i);
        }
      }

      // Add the failed step
      this.steps.push(failedStep);
      this.currentStep = failedStep;
      this.currentStepIndex = this.steps.length - 1;

      // Ensure the failed step is expanded
      this.collapsedSteps.delete(this.currentStepIndex);

      // Start the typewriter animation for the failed step
      this.startTypewriterAnimation(this.currentStepIndex);

      this.stepUpdated.emit(this.currentStepIndex);

      // Trigger change detection for OnPush
      this.cdr.markForCheck();

      // Ensure smooth scrolling to the failed step
      setTimeout(() => {
        const stepElements = document.querySelectorAll('.stepper-item');
        if (stepElements && stepElements.length > 0) {
          const lastStep = stepElements[stepElements.length - 1] as HTMLElement;
          if (lastStep) {
            lastStep.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
          }
        }
      }, 100);
    } else {
      // We already have a failure step, make sure it has the correct description
      const failedStep = this.steps[failedStepIndex];

      // Always update the description for failed steps to ensure it shows the latest error
      failedStep.description = formattedDescription;
      failedStep.visibleTitle = ''; // Reset visible title
      failedStep.visibleDescription = ''; // Reset visible description
      failedStep.isTitleTyping = true; // Set title typing state
      failedStep.isTyping = true; // Set typing state

      // Make this step active
      failedStep.active = true;
      failedStep.completed = false;

      // Update current step reference
      this.currentStep = failedStep;
      this.currentStepIndex = failedStepIndex;

      // Smart collapse: Only collapse steps that are not user-expanded
      for (let i = 0; i < this.steps.length; i++) {
        if (i !== failedStepIndex && this.userExpandedStep !== i) {
          this.collapsedSteps.add(i);
        }
      }

      // Ensure the failed step is expanded
      this.collapsedSteps.delete(failedStepIndex);

      // Start typewriter animation
      this.startTypewriterAnimation(failedStepIndex);

      // Emit step updated event
      this.stepUpdated.emit(failedStepIndex);

      // Trigger change detection for OnPush
      this.cdr.markForCheck();
    }
  }

  /**
   * Gets the display title for a progress state
   * @param progress The progress state
   * @returns The display title for the progress state
   */
  public getDisplayTitleForProgress(progress: string): string {
    // Check if this is a known stepper state
    if (Object.values(StepperState).includes(progress as StepperState)) {
      return this.stepperStateMap[progress as StepperState];
    }

    // If not a known state, return the original progress value
    return progress;
  }

  /**
   * Determines if a step is a failure step
   * @param step The step to check
   * @returns True if the step is a failure step, false otherwise
   */
  isFailureStep(step: StepperItem): boolean {
    // Check if the step title contains 'Failed' or matches the BUILD_FAILED display title
    // Also check if the status is FAILED and this is the current step
    return step.title.includes('Failed') ||
           step.title === this.getDisplayTitleForProgress(StepperState.BUILD_FAILED) ||
           ((this.status === 'FAILED' || this.status === 'failed') && step === this.currentStep);
  }

  /**
   * Handles retry button click for a failed step
   * ENHANCED: Implements proper sequential retry flow with state management
   * @param index The index of the step to retry
   */
  onRetryClick(index: number, event?: Event): void {
    // Stop event propagation if provided
    if (event) {
      event.stopPropagation();
    }

    // Check if retry is already in progress
    if (this.isRetryInProgress()) {
      this.logger.info('🔄 Retry already in progress, ignoring duplicate click');
      return;
    }

    // Get the step
    const step = this.steps[index];

    // Initialize retryCount if it doesn't exist
    if (step.retryCount === undefined) {
      step.retryCount = 0;
    }

    // Check retry attempt limits
    if (step.retryCount >= this.maxRetryAttempts) {
      this.logger.error('❌ Max retry attempts reached for this step');
      return;
    }

    // Increment retry count
    step.retryCount++;

    this.logger.info('Retry button clicked with enhanced state management', {
      stepIndex: index,
      stepTitle: step.title,
      retryCount: step.retryCount,
      maxRetryAttempts: this.maxRetryAttempts,
      progress: this.progress,
      status: this.status,
      isRetryInProgress: this.isRetryInProgress()
    });

    // Check if this is a BUILD or DEPLOY failure and we need to report the error
    const isBuildOrDeployFailure = this.progress === StepperState.BUILD ||
                                  this.progress === StepperState.BUILD_FAILED ||
                                  this.progress === StepperState.DEPLOY ||
                                  this.progress === StepperState.DEPLOYED ||
                                  step.title.toLowerCase().includes('build') ||
                                  step.title.toLowerCase().includes('deploy');



    // Debug logging to identify why condition might fail
    this.logger.info('🔍 Retry condition evaluation:', {
      isBuildOrDeployFailure,
      progress: this.progress,
      status: this.status,
      statusTrimmed: this.status?.trim().toUpperCase(),
      useApi: this.useApi,
      hasProjectId: !!this.projectId,
      hasJobId: !!this.jobId,
      projectId: this.projectId,
      jobId: this.jobId,
      stepTitle: step.title
    });

    if (isBuildOrDeployFailure && (this.status?.trim().toUpperCase() === 'FAILED' || this.status?.trim().toLowerCase() === 'failed') && this.useApi && this.projectId && this.jobId) {
      this.logger.info('✅ Triggering BUILD/DEPLOY retry with proper sequential flow');
      // Set retry in progress before starting the sequential flow
      this.isRetryInProgress.set(true);
      this.handleBuildRetryWithErrorReporting(index, step);
    } else {
      this.logger.warn('❌ Using regular retry instead of error reporting retry', {
        reason: !isBuildOrDeployFailure ? 'Not BUILD/DEPLOY failure' :
                this.status?.trim().toUpperCase() !== 'FAILED' ? `Status not FAILED (actual: '${this.status}')` :
                !this.useApi ? 'useApi is false' :
                !this.projectId ? 'No projectId' :
                !this.jobId ? 'No jobId' : 'Unknown reason'
      });
      this.handleRegularRetry(index, step);
    }
  }

  /**
   * Handle retry for BUILD and DEPLOY failures with sequential error reporting
   * ENHANCEMENT: Sequential retry mechanism - calls /code-generation/error/build first, then SSE
   * @param index The step index
   * @param step The step being retried
   */
  private handleBuildRetryWithErrorReporting(index: number, step: StepperItem): void {
    this.logger.info('🚀 Handling BUILD/DEPLOY retry with sequential error reporting', {
      projectId: this.projectId,
      jobId: this.jobId,
      stepTitle: step.title,
      progress: this.progress,
      status: this.status,
      useApi: this.useApi,
      hasProjectId: !!this.projectId,
      hasJobId: !!this.jobId,
      projectIdLength: this.projectId?.length || 0,
      jobIdLength: this.jobId?.length || 0
    });

    // Extract error message from service (backward compatibility)
    let errorMessage = this.pollingService.getErrorMessageFromLastResponse();
    const lastResponse = this.pollingService.getLastStatusResponse();

    this.logger.info('🔍 Error message extraction result for sequential retry:', {
      hasErrorMessage: !!errorMessage,
      errorMessageLength: errorMessage?.length || 0,
      errorMessagePreview: errorMessage?.substring(0, 100) || 'No message',
      hasLastResponse: !!lastResponse,
      lastResponseKeys: lastResponse ? Object.keys(lastResponse) : [],
      lastResponseDetails: lastResponse?.details ? Object.keys(lastResponse.details) : [],
      lastResponseLog: lastResponse?.details?.log ? 'Has log field' : 'No log field',
      endpoint: '/code-generation/error/build'
    });

    // ENHANCEMENT: Fallback to SSE data if polling service doesn't have error message
    // This ensures retry works in SSE-only environments
    if (!errorMessage) {
      this.logger.info('🔄 No error message from polling service, attempting SSE data fallback');
      errorMessage = this.extractErrorMessageFromSSEData();

      if (errorMessage) {
        this.logger.info('✅ Successfully extracted error message from SSE data fallback:', {
          errorMessageLength: errorMessage.length,
          errorMessagePreview: errorMessage.substring(0, 100) + '...'
        });
      }
    }

    if (!errorMessage) {
      this.logger.warn('❌ No error message found from polling service or SSE data, proceeding without error reporting');
      this.handleRegularRetry(index, step);
      return;
    }

    // Update UI to show retry is in progress
    this.updateStepForRetry(index, step);

    // Report the error to the code-generation backend first (Sequential Retry Step 1)
    this.logger.info('📡 About to call code-generation error reporting API (Sequential Retry Step 1)', {
      projectId: this.projectId,
      jobId: this.jobId,
      errorMessage: errorMessage.substring(0, 100) + '...',
      endpoint: '/code-generation/error/build',
      apiCall: `POST /code-generation/error/build?projectid=${this.projectId}&status_id=${this.jobId}`,
      payloadPreview: { error: errorMessage.substring(0, 50) + '...' }
    });

    // ENHANCEMENT: Extract code files from service for error reporting (backward compatibility)
    const codeFiles = this.extractCodeFilesForErrorReporting();

    // CRITICAL: Sequential retry flow - Step 1: Call /code-generation/error/build API first
    this.logger.info('🔄 Starting sequential retry flow: Step 1 - Code-generation error API call');

    this.errorReportingService.reportCodeGenerationBuildError(this.projectId, this.jobId, errorMessage, codeFiles)
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        // STEP 2: Wait for code-generation API success before proceeding to SSE
        // switchMap(apiResponse => {
        //   if (apiResponse.success) {
        //     this.logger.info('✅ Code-generation error recovery API completed successfully, proceeding to Step 2 - SSE restart');
        //     // STEP 3: Only after API success, restart SSE monitoring
        //     // return this.restartSSEMonitoringAfterError(index);
        //   } else {
        //     this.logger.error('❌ Code-generation error recovery API failed, aborting sequential retry');
        //     throw new Error('Code-generation error recovery API failed');
        //   }
        // }),
        catchError(error => {
          this.logger.error('❌ Sequential retry flow failed:', error);
          // Fallback to regular retry if sequential flow fails
          this.logger.info('🔄 Falling back to regular retry mechanism');
          this.proceedWithRetry(index);
          return of(false);
        })
      )
      .subscribe({
        next: (success) => {
          if (success) {
            this.logger.info('✅ Complete sequential retry flow successful: API → SSE restart');
          } else {
            this.logger.warn('⚠️ Sequential retry completed with fallback to regular retry');
          }
          // Reset retry state after completion
          this.isRetryInProgress.set(false);
        },
        error: (error) => {
          this.logger.error('❌ Sequential retry flow error:', error);
          // Final fallback to regular retry
          this.proceedWithRetry(index);
          // Reset retry state after error
          this.isRetryInProgress.set(false);
        }
      });
  }

  /**
   * Restart SSE monitoring after successful error recovery API call
   * CRITICAL: Only called after error API succeeds
   * @param stepIndex The step index being retried
   */
  // private restartSSEMonitoringAfterError(stepIndex: number): Observable<boolean> {
  //   this.logger.info('🔄 Restarting SSE monitoring after successful error recovery');

  //   // Stop current SSE monitoring
  //   // this.stopApiMonitoring();

  //   // Wait a moment for cleanup
  //   return timer(1000).pipe(
  //     switchMap(() => {
  //       // Restart SSE monitoring
  //       if (this.projectId && this.jobId && this.useApi) {
  //         this.logger.info('🚀 Starting fresh SSE monitoring after error recovery');

  //         // Start enhanced SSE monitoring
  //         this.startApiMonitoring();

  //         // Emit retry event to parent component
  //         this.retryStep.emit(stepIndex);

  //         // Reset error state
  //         this.resetErrorState();

  //         return of(true);
  //       } else {
  //         this.logger.warn('⚠️ Cannot restart SSE monitoring: missing required parameters');
  //         return of(false);
  //       }
  //     }),
  //     catchError(error => {
  //       this.logger.error('❌ Failed to restart SSE monitoring:', error);
  //       return of(false);
  //     })
  //   );
  // }

  /**
   * Handle regular retry (non-BUILD failures)
   * @param index The step index
   * @param step The step being retried
   */
  private handleRegularRetry(index: number, step: StepperItem): void {
    this.logger.info('Handling regular retry', {
      stepTitle: step.title,
      retryCount: step.retryCount
    });

    // Update UI to show retry is in progress
    this.updateStepForRetry(index, step);

    // Proceed with retry immediately
    this.proceedWithRetry(index);
  }

  /**
   * Update step UI for retry state
   * @param index The step index
   * @param step The step being retried
   */
  private updateStepForRetry(index: number, step: StepperItem): void {
    // Change status back to IN_PROGRESS to indicate retry is happening
    this.status = 'in-progress';

    // Update the step's properties to show it's being retried
    step.completed = false;
    step.active = true;
    step.isRetrying = true; // Set the isRetrying flag for shimmer effect

    // Expand the step if it's collapsed
    this.collapsedSteps.delete(index);

    // Trigger change detection for OnPush
    this.cdr.markForCheck();
  }

  /**
   * Proceed with the actual retry process
   * @param index The step index
   */
  private proceedWithRetry(index: number): void {
    // Emit retry event with step index after a short delay
    // This matches the behavior in error-page component
    setTimeout(() => {
      // Emit the retry event to the parent component
      this.retryStep.emit(index);

      // Reset the error state in the stepper
      this.resetErrorState();

      // If using API, restart SSE monitoring from the current step
      if (this.useApi && this.projectId && this.jobId) {
        this.restartApiMonitoringFromCurrentStep();
      }
    }, 500);
  }

  /**
   * Restart real-time monitoring after a retry operation
   *
   * @description Cleanly restarts SSE connection with the same configuration
   * @ensures Identical monitoring setup as the original connection
   * @used_for Retry operations that require fresh SSE connection
   */
  private restartApiMonitoringFromCurrentStep(): void {
    this.logger.info('Restarting SSE connection after retry', {
      projectId: this.projectId,
      jobId: this.jobId,
      note: 'Restarting with identical configuration as original connection'
    });

    // Stop current SSE connection
    this.stopApiMonitoring();

    // Start fresh SSE connection with same configuration
    this.startApiMonitoring();
  }

  /**
   * Resets the error state in the stepper
   * This is called when a retry is initiated
   */
  private resetErrorState(): void {
    // Change status to in-progress
    this.status = 'in-progress';

    // Reset any error-related properties
    // Find the step that is currently being retried
    const retryingStepIndex = this.steps.findIndex(step => step.isRetrying);
    if (retryingStepIndex >= 0) {
      // Keep the shimmer effect for a short time to provide visual feedback
      setTimeout(() => {
        // Reset the isRetrying flag after a delay
        this.steps[retryingStepIndex].isRetrying = false;
        // Trigger change detection for OnPush
        this.cdr.markForCheck();
      }, 3000); // Keep shimmer for 3 seconds
    }

    // Trigger change detection for OnPush
    this.cdr.markForCheck();
  }

  /**
   * Checks if a step has reached the maximum retry attempts
   * @param index The index of the step to check
   * @returns True if the step has reached the maximum retry attempts
   */
  hasReachedMaxRetries(index: number): boolean {
    if (index < 0 || index >= this.steps.length) {
      return false;
    }
    const step = this.steps[index];
    // If retryCount is undefined, treat it as 0
    const retryCount = step.retryCount || 0;
    return retryCount >= 3;
  }

  /**
   * Debug method to check if the retry button should be visible
   * @param step The step to check
   * @param index The index of the step
   * @returns True if the retry button should be visible
   */
  shouldShowRetryButton(step: StepperItem, index: number): boolean {
    const isFailure = this.isFailureStep(step);
    const notMaxRetries = !this.hasReachedMaxRetries(index);
    return isFailure && notMaxRetries;
  }

  /**
   * Debug method to create a failed step for testing
   * This method can be called from the parent component to test the retry button
   */
  public createFailedStepForTesting(): void {
    // Set status to FAILED
    this.status = 'FAILED';

    // Create a failed step
    const failedStep: StepperItem = {
      title: 'Test Failed',
      description: 'This is a test failed step for testing the retry button.',
      visibleTitle: 'Test Failed',
      visibleDescription: 'This is a test failed step for testing the retry button.',
      completed: false,
      active: true,
      isTitleTyping: false,
      isTyping: false,
      retryCount: 0,
      startTime: Date.now(),
      elapsedTime: 0,
      timerActive: false
    };

    // Add the failed step
    this.steps.push(failedStep);
    this.currentStep = failedStep;
    this.currentStepIndex = this.steps.length - 1;

    // Expand only the failed step
    this.collapsedSteps.delete(this.currentStepIndex);

    // Trigger change detection for OnPush
    this.cdr.markForCheck();
  }

  /**
   * Checks if a step is currently in processing state
   * @param index The index of the step to check
   * @returns True if the step is in processing state
   */
  isProcessingStep(index: number): boolean {
    // A step is processing if:
    // 1. The status is IN_PROGRESS
    // 2. This step is the current step
    // 3. The current step is not completed
    // 4. The step is not a failure step

    // First check if we have a valid current step
    if (!this.currentStep || index < 0 || index >= this.steps.length) {
      return false;
    }

    return (
      (this.status === 'IN_PROGRESS' || this.status === 'in-progress') &&
      index === this.currentStepIndex &&
      !this.currentStep.completed &&
      !this.isFailureStep(this.steps[index])
    );
  }

  /**
   * Checks if a step is a processed step (completed but not the current processing step)
   * @param index The index of the step to check
   * @returns True if the step is a processed step
   */
  isProcessedStep(index: number): boolean {
    // A step is processed if:
    // 1. It has a valid index
    // 2. It is completed
    // 3. It is not the current processing step

    if (index < 0 || index >= this.steps.length) {
      return false;
    }

    const step = this.steps[index];
    return step.completed && !this.isProcessingStep(index);
  }

  /**
   * Helper method to add a final step (completed or build succeeded)
   * @param step The step to add
   */
  private addFinalStep(step: StepperItem): void {
    // Mark previous step as completed
    const prevStepIndex = this.steps.length - 1;
    this.steps[prevStepIndex].completed = true;
    this.steps[prevStepIndex].active = false;
    // Stop the timer for the previous step and capture completion time
    this.stopStepTimer(prevStepIndex);

    // Smart collapse: Collapse the previous step when it completes,
    // but only if it's not user-expanded
    if (this.userExpandedStep !== prevStepIndex) {
      this.collapsedSteps.add(prevStepIndex);
    }

    // Smart collapse: Collapse other steps that are not user-expanded
    for (let i = 0; i < this.steps.length; i++) {
      if (i !== prevStepIndex && this.userExpandedStep !== i) {
        this.collapsedSteps.add(i);
      }
    }

    // Add the step
    this.steps.push(step);
    this.currentStep = step;
    this.currentStepIndex = this.steps.length - 1;

    // Ensure the final step is expanded
    this.collapsedSteps.delete(this.currentStepIndex);

    // Start the typewriter animation for the new step
    this.startTypewriterAnimation(this.currentStepIndex);

    this.stepUpdated.emit(this.currentStepIndex);

    // Ensure smooth scrolling to the new step
    setTimeout(() => {
      const stepElements = document.querySelectorAll('.stepper-item');
      if (stepElements && stepElements.length > 0) {
        const lastStep = stepElements[stepElements.length - 1] as HTMLElement;
        if (lastStep) {
          lastStep.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
      }
    }, 100);
  }

  /**
   * Extract code files from service for error reporting (backward compatibility)
   * ENHANCEMENT: Gets code files from the same source used by Monaco editor
   * Uses the response processor service as primary source and code-sharing service as fallback
   * @returns Array of code files in the format expected by error reporting service
   */
  private extractCodeFilesForErrorReporting(): CodeFileForErrorReport[] {
    try {
      // PRIMARY SOURCE: Get code files from the response processor service
      // This is the same source that the code-viewer/Monaco editor uses
      const currentCodeFiles = this.newPollingResponseProcessor.getCurrentCodeFiles();

      if (currentCodeFiles && currentCodeFiles.length > 0) {
        // Convert FileData format to CodeFileForErrorReport format
        const codeFiles: CodeFileForErrorReport[] = currentCodeFiles.map((file: any) => ({
          fileName: file.path || file.fileName || 'unknown.txt',
          content: file.code || file.content || ''
        }));

        this.logger.info(`📂 Extracted ${codeFiles.length} code files from response processor for error reporting`, {
          files: codeFiles.map((f: CodeFileForErrorReport) => ({ name: f.fileName, contentLength: f.content.length }))
        });

        return codeFiles;
      }

      // FALLBACK SOURCE: Try to get code files from the code-sharing service
      // This service stores the generated code that's displayed in the Monaco editor
      const generatedCode = this.codeSharingService.getGeneratedCode();

      if (generatedCode && Array.isArray(generatedCode) && generatedCode.length > 0) {
        // Convert generated code format to CodeFileForErrorReport format
        const codeFiles: CodeFileForErrorReport[] = generatedCode.map((file: any) => ({
          fileName: file.fileName || file.name || 'unknown.txt',
          content: file.content || ''
        }));

        this.logger.info(`📂 Extracted ${codeFiles.length} code files from code-sharing service for error reporting (fallback)`, {
          files: codeFiles.map((f: CodeFileForErrorReport) => ({ name: f.fileName, contentLength: f.content.length }))
        });

        return codeFiles;
      }

      // LEGACY FALLBACK: Try the service method as last resort (backward compatibility)
      const latestResponse = this.pollingService.getLastStatusResponse();

      if (latestResponse && latestResponse.files && Array.isArray(latestResponse.files)) {
        // Convert FileData format to CodeFileForErrorReport format
        const codeFiles: CodeFileForErrorReport[] = latestResponse.files.map((file: any) => ({
          fileName: file.path || file.fileName || 'unknown.txt',
          content: file.code || file.content || ''
        }));

        this.logger.info(`📂 Extracted ${codeFiles.length} code files from legacy service for error reporting (legacy fallback)`, {
          files: codeFiles.map((f: CodeFileForErrorReport) => ({ name: f.fileName, contentLength: f.content.length }))
        });

        return codeFiles;
      }

      this.logger.warn('⚠️ No code files found in any available sources for error reporting');
    } catch (error) {
      this.logger.warn('⚠️ Failed to extract code files for error reporting:', error);
    }

    // Return empty array if no files could be extracted
    return [];
  }

  /**
   * Extracts error message from a log field
   * @param logContent The log content to parse
   * @returns The extracted error message or empty string
   */
  private extractErrorMessage(logContent: string): string {
    if (!logContent) return '';

    try {
      // Check if the log content is a JSON string
      if (typeof logContent === 'string' && logContent.includes('{') && logContent.includes('}')) {
        // Try to parse the log content as JSON
        const parsedLog = JSON.parse(logContent);

        // If it has a message property, use that
        if (parsedLog.message) {
          return parsedLog.message;
        }

        // If it has an error property, use that
        if (parsedLog.error) {
          return parsedLog.error;
        }

        // If it has a data property with an error message, use that
        if (parsedLog.data && typeof parsedLog.data === 'string' &&
            (parsedLog.data.includes('error') || parsedLog.data.includes('Error') ||
             parsedLog.data.includes('failed') || parsedLog.data.includes('Failed'))) {
          return parsedLog.data;
        }
      }
    } catch (e) {
    }

    // If we couldn't extract an error message, return the original log content
    return logContent;
  }

  /**
   * Starts the typewriter animation for a step with progressive markdown rendering
   * @param stepIndex The index of the step to animate
   */
  private startTypewriterAnimation(stepIndex: number): void {
    if (stepIndex < 0 || stepIndex >= this.steps.length) {
      return;
    }

    const step = this.steps[stepIndex];

    // Clear any existing typing animations for this step
    if (this.timeoutRefs[`typing-title-${stepIndex}`]) {
      clearTimeout(this.timeoutRefs[`typing-title-${stepIndex}`]);
    }
    if (this.timeoutRefs[`typing-desc-${stepIndex}`]) {
      clearTimeout(this.timeoutRefs[`typing-desc-${stepIndex}`]);
    }

    // Reset visible text if needed
    if (!step.visibleTitle) {
      step.visibleTitle = '';
    }
    if (!step.visibleDescription) {
      step.visibleDescription = '';
    }

    // Start title typing first
    this.startTitleTypewriter(stepIndex);
  }

  /**
   * Starts typewriter animation for the step title
   * @param stepIndex The index of the step to animate
   */
  private startTitleTypewriter(stepIndex: number): void {
    const step = this.steps[stepIndex];

    if (!step.isTitleTyping || step.visibleTitle === step.title) {
      // Title is complete, start description
      this.startDescriptionTypewriter(stepIndex);
      return;
    }

    const typeTitleChar = () => {
      if (!step.isTitleTyping) {
        return;
      }

      const currentLength = step.visibleTitle.length;
      const fullTitle = step.title;

      if (currentLength < fullTitle.length) {
        const nextChar = fullTitle.charAt(currentLength);
        step.visibleTitle = fullTitle.substring(0, currentLength + 1);

        // Ultra-fast and fluid typing for titles
        let nextDelay = this.typingSpeed * 0.2;

        // Minimal pauses for natural flow - optimized for ultra-fast typing
        if (['.', '!', '?'].includes(nextChar)) {
          nextDelay = this.typingSpeed * 0.5;
        } else if ([',', ';', ':'].includes(nextChar)) {
          nextDelay = this.typingSpeed * 0.3;
        } else if (nextChar === ' ') {
          nextDelay = this.typingSpeed * 0.2;
        }

        this.cdr.markForCheck();
        this.timeoutRefs[`typing-title-${stepIndex}`] = setTimeout(typeTitleChar, nextDelay);
      } else {
        // Title complete, start description with minimal pause for fluid flow
        step.isTitleTyping = false;
        this.cdr.markForCheck();
        setTimeout(() => {
          this.startDescriptionTypewriter(stepIndex);
        }, this.typingSpeed * 0.3);
      }
    };

    this.timeoutRefs[`typing-title-${stepIndex}`] = setTimeout(typeTitleChar, this.typingSpeed);
  }

  /**
   * Starts typewriter animation for the step description with markdown support
   * @param stepIndex The index of the step to animate
   */
  private startDescriptionTypewriter(stepIndex: number): void {
    const step = this.steps[stepIndex];

    if (!step.isTyping || step.visibleDescription === step.description) {
      return;
    }

    const typeDescChar = () => {
      if (!step.isTyping) {
        return;
      }

      const currentLength = step.visibleDescription.length;
      const fullText = step.description;

      if (currentLength < fullText.length) {
        const nextChar = fullText.charAt(currentLength);
        step.visibleDescription = fullText.substring(0, currentLength + 1);

        // Ultra-fast and fluid typing for descriptions
        let nextDelay = this.typingSpeed * 0.4;

        // Minimal pauses for smooth flow - optimized for ultra-fast typing
        if (['.', '!', '?'].includes(nextChar)) {
          nextDelay = this.typingSpeed * 0.6;
        } else if ([',', ';', ':'].includes(nextChar)) {
          nextDelay = this.typingSpeed * 0.5;
        } else if (nextChar === ' ') {
          nextDelay = this.typingSpeed * 0.3;
        }

        // Trigger change detection for progressive markdown rendering
        this.cdr.markForCheck();
        this.timeoutRefs[`typing-desc-${stepIndex}`] = setTimeout(typeDescChar, nextDelay);
      } else {
        // Description typing complete
        step.isTyping = false;
        this.cdr.markForCheck();
      }
    };

    this.timeoutRefs[`typing-desc-${stepIndex}`] = setTimeout(typeDescChar, this.typingSpeed);
  }

  /**
   * Start the timer for the current active step
   */
  private startTimer(): void {
    // Stop any existing timer
    this.stopTimer();

    // Start a new timer that updates every second
    this.timerInterval = setInterval(() => {
      this.updateTimers();
    }, this.timerUpdateInterval);
  }

  /**
   * Stop the timer
   */
  private stopTimer(): void {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
      this.timerInterval = null;
    }
  }

  /**
   * Update the elapsed time for all active timers
   */
  private updateTimers(): void {
    const currentTime = Date.now();
    let hasActiveTimers = false;

    this.steps.forEach(step => {
      if (step.timerActive && step.startTime) {
        step.elapsedTime = Math.floor((currentTime - step.startTime) / 1000);
        hasActiveTimers = true;
      }
    });

    // Stop the timer if no steps have active timers
    if (!hasActiveTimers) {
      this.stopTimer();
    }

    // Trigger change detection for OnPush
    this.cdr.markForCheck();
  }

  /**
   * Stop the timer for a specific step and capture completion time
   */
  private stopStepTimer(stepIndex: number): void {
    if (stepIndex >= 0 && stepIndex < this.steps.length) {
      const step = this.steps[stepIndex];
      if (step.timerActive && step.startTime) {
        // Capture the final completion time
        step.completionTime = Math.floor((Date.now() - step.startTime) / 1000);
        step.elapsedTime = step.completionTime; // Ensure elapsedTime matches completion time
      }
      step.timerActive = false;
    }
  }

  /**
   * Format elapsed time into a readable string (M:SS format like 0:06)
   */
  /**
   * Extract error message from SSE data as fallback when polling service doesn't have it
   * ENHANCEMENT: Ensures retry mechanism works in SSE-only environments
   * @returns Error message from SSE data or empty string if not found
   */
  private extractErrorMessageFromSSEData(): string {
    try {
      this.logger.info('🔍 Attempting to extract error message from SSE data processor');

      // Get current SSE processor state
      const sseState = this.sseDataProcessor.getCurrentState();

      this.logger.debug('📊 SSE processor state for error extraction:', {
        status: sseState.status,
        progress: sseState.progress,
        progressDescription: sseState.progressDescription,
        logsCount: sseState.logsCount,
        hasPollingProcessorState: !!sseState.pollingProcessorState
      });

      // Check if we have a FAILED status with error information
      if (sseState.status === 'FAILED' || sseState.status === 'failed') {
        // Try to extract from progress description first
        if (sseState.progressDescription &&
            (sseState.progressDescription.toLowerCase().includes('error') ||
             sseState.progressDescription.toLowerCase().includes('failed') ||
             sseState.progressDescription.toLowerCase().includes('unexpected'))) {
          this.logger.info('✅ Found error message in SSE progress description');
          return sseState.progressDescription;
        }

        // Fallback: Use a generic error message for FAILED status
        const genericMessage = 'Build process failed. Please check the logs for more details.';
        this.logger.info('⚠️ Using generic error message for FAILED status from SSE data');
        return genericMessage;
      }

      this.logger.warn('❌ No error information found in SSE data');
      return '';

    } catch (error) {
      this.logger.error('❌ Error extracting error message from SSE data:', error);
      return '';
    }
  }

  formatElapsedTime(elapsedTime: number): string {
    const minutes = Math.floor(elapsedTime / 60);
    const seconds = elapsedTime % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }

  // ENHANCED: Progress description isolation methods using Angular 19+ Signals

  /**
   * Update step description in signal-based state management
   * @param progressState The progress state
   * @param description The description to store
   */
  private updateStepDescription(progressState: string, description: string): void {
    this.currentProgressState.set(progressState);

    const currentDescriptions = this.stepDescriptions();
    const newDescriptions = new Map(currentDescriptions);
    newDescriptions.set(progressState, description);
    this.stepDescriptions.set(newDescriptions);
  }

  /**
   * Freeze step description to prevent future overwrites
   * @param stepIndex The index of the step to freeze
   */
  private freezeStepDescription(stepIndex: number): void {
    if (stepIndex >= 0 && stepIndex < this.steps.length) {
      const step = this.steps[stepIndex];
      step.frozenDescription = true;
      step.descriptionFrozenAt = Date.now();

      // Store in frozen descriptions signal
      if (step.originalProgressState) {
        const currentFrozen = this.frozenStepDescriptions();
        const newFrozen = new Map(currentFrozen);
        newFrozen.set(step.originalProgressState, step.description);
        this.frozenStepDescriptions.set(newFrozen);
      }
    }
  }

  /**
   * Check if a step description should be preserved (frozen)
   * @param stepIndex The index of the step to check
   * @returns True if the description should be preserved
   */
  private shouldPreserveStepDescription(stepIndex: number): boolean {
    if (stepIndex >= 0 && stepIndex < this.steps.length) {
      const step = this.steps[stepIndex];
      return step.completed || step.frozenDescription || false;
    }
    return false;
  }

  /**
   * Get preserved description for a progress state
   * @param progressState The progress state
   * @returns The preserved description if it exists
   */
  private getPreservedDescription(progressState: string): string | undefined {
    const frozenDescriptions = this.frozenStepDescriptions();
    return frozenDescriptions.get(progressState);
  }

  /**
   * Log current step state for debugging
   */
  private logStepState(): void {
    const stateInfo = this.stepStateChanges();
  }
}
