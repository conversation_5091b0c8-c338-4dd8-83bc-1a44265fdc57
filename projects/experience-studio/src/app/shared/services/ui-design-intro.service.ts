import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, combineLatest, forkJoin, of } from 'rxjs';
import { map, catchError, tap, finalize, switchMap } from 'rxjs/operators';
import { createLogger } from '../utils/logger';
import { GenerateUIDesignService, IntroCodeItem } from './generate-ui-design.service';
import { ToastService } from './toast.service';

export interface IntroMessageState {
  isLoading: boolean;
  text: string;
  isTyping: boolean;
  hasError: boolean;
  errorMessage?: string;
  shouldReplaceText: boolean; // Flag to indicate if text should replace existing message
  targetMessageId?: string; // ID of the message to replace text in

  // Enhanced loading state management
  introAPICompleted: boolean; // Whether intro API has completed successfully
  mainAPIInProgress: boolean; // Whether main wireframe generation API is still running
  showLoadingIndicator: boolean; // Whether to show loading spinner/indicator
  loadingPhase: 'intro' | 'main' | 'completed' | 'error'; // Current phase of the generation process

  // Message type distinction
  messageType?: 'generation' | 'regeneration'; // Type of operation for different display logic
}

export interface ParallelAPIResult {
  introText: string;
  mainAPIResult: any;
  introSuccess: boolean;
  mainAPISuccess: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class UIDesignIntroService {
  private logger = createLogger('UIDesignIntroService');

  // State management for intro messages
  private introMessageStateSubject = new BehaviorSubject<IntroMessageState>({
    isLoading: false,
    text: '',
    isTyping: false,
    hasError: false,
    shouldReplaceText: false,
    introAPICompleted: false,
    mainAPIInProgress: false,
    showLoadingIndicator: false,
    loadingPhase: 'intro',
    messageType: 'generation'
  });

  public introMessageState$ = this.introMessageStateSubject.asObservable();

  // Typewriter effect state
  private typewriterTimeouts: any[] = [];
  private readonly typingSpeed = 3; // milliseconds per character - ultra-fast typing effect

  constructor(
    private generateUIDesignService: GenerateUIDesignService,
    private toastService: ToastService
  ) {}

  /**
   * Execute parallel API calls for initial UI Design generation with text replacement
   * @param userRequest - The user's prompt
   * @param mainAPICall - Observable for the main generation API
   * @param targetMessageId - ID of the chat message to replace text in
   * @returns Observable<ParallelAPIResult>
   */
  executeParallelGeneration(
    userRequest: string,
    mainAPICall: Observable<any>,
    targetMessageId?: string
  ): Observable<ParallelAPIResult> {
    this.logger.info('🚀 Starting parallel API calls for initial generation with dynamic loading state management');

    // Initialize loading state for intro phase
    this.updateIntroState({
      isLoading: true,
      text: '',
      isTyping: false,
      hasError: false,
      shouldReplaceText: !!targetMessageId,
      targetMessageId,
      introAPICompleted: false,
      mainAPIInProgress: true,
      showLoadingIndicator: true,
      loadingPhase: 'intro',
      messageType: 'generation'
    });

    // Create intro API call (empty code array for initial generation)
    const introAPICall = this.generateUIDesignService.generateIntroMessage(userRequest, []);

    // Execute intro API first, then handle main API separately for better loading state control
    return introAPICall.pipe(
      tap(introText => {
        this.logger.info('🎭 Intro API completed successfully');
        this.logger.info('🎭 Intro text received:', {
          length: introText?.length || 0,
          preview: introText?.substring(0, 100) + (introText?.length > 100 ? '...' : ''),
          type: typeof introText
        });

        // Validate and sanitize intro text
        const validatedIntroText = this.validateIntroText(introText);

        // Update state to show intro API completed, main API still in progress
        this.updateIntroState({
          isLoading: false,
          text: validatedIntroText,
          isTyping: false,
          hasError: false,
          shouldReplaceText: !!targetMessageId,
          targetMessageId,
          introAPICompleted: true,
          mainAPIInProgress: true,
          showLoadingIndicator: true,
          loadingPhase: 'main'
        });

        // Start text replacement with typewriter effect
        if (targetMessageId) {
          this.startTextReplacementWithLoading(validatedIntroText, targetMessageId);
        } else {
          this.startTypewriterEffectWithLoading(validatedIntroText);
        }
      }),
      switchMap(introText => {
        // Now execute main API and combine results
        return mainAPICall.pipe(
          map(mainAPIResult => ({
            introText,
            mainAPIResult,
            introSuccess: true,
            mainAPISuccess: true
          })),
          tap(result => {
            this.logger.info('🎉 Main API completed successfully - updating loading state');

            // Update state to show both APIs completed
            this.updateIntroState({
              isLoading: false,
              text: this.validateIntroText(result.introText),
              isTyping: false,
              hasError: false,
              shouldReplaceText: !!targetMessageId,
              targetMessageId,
              introAPICompleted: true,
              mainAPIInProgress: false,
              showLoadingIndicator: false,
              loadingPhase: 'completed'
            });

            this.logger.info('✅ All APIs completed - loading indicators removed');
          })
        );
      }),
      catchError(error => {
        this.logger.error('❌ API calls failed:', error);

        // Update loading state to show error
        this.updateIntroState({
          isLoading: false,
          text: 'Unable to prepare design context. Please try again.',
          isTyping: false,
          hasError: true,
          shouldReplaceText: !!targetMessageId,
          targetMessageId,
          introAPICompleted: false,
          mainAPIInProgress: false,
          showLoadingIndicator: false,
          loadingPhase: 'error',
          errorMessage: 'API call failed'
        });

        // Handle partial failures gracefully
        return this.handleParallelAPIError(error, userRequest, mainAPICall);
      })
    );
  }

  /**
   * Execute parallel API calls for UI Design regeneration with text replacement
   * @param userRequest - The user's prompt
   * @param selectedNodes - Array of selected UI Design nodes
   * @param mainAPICall - Observable for the main regeneration API
   * @param targetMessageId - ID of the chat message to replace text in
   * @returns Observable<ParallelAPIResult>
   */
  executeParallelRegeneration(
    userRequest: string,
    selectedNodes: any[],
    mainAPICall: Observable<any>,
    targetMessageId?: string
  ): Observable<ParallelAPIResult> {
    this.logger.info('🔄 Starting parallel API calls for regeneration with dynamic loading state management');
    this.logger.info('Selected nodes count:', selectedNodes.length);

    // Initialize loading state for regeneration intro phase
    this.updateIntroState({
      isLoading: true,
      text: '',
      isTyping: false,
      hasError: false,
      shouldReplaceText: !!targetMessageId,
      targetMessageId,
      introAPICompleted: false,
      mainAPIInProgress: true,
      showLoadingIndicator: true,
      loadingPhase: 'intro',
      messageType: 'regeneration'
    });

    // Convert selected nodes to code items for intro API
    const codeItems: IntroCodeItem[] = this.convertNodesToCodeItems(selectedNodes);

    // Create intro API call with selected node data
    const introAPICall = this.generateUIDesignService.generateIntroMessage(userRequest, codeItems);

    // Execute intro API first for regeneration, then handle main API separately
    return introAPICall.pipe(
      tap(introText => {
        this.logger.info('🎭 Regeneration intro API completed successfully');
        this.logger.info('🎭 Regeneration intro text received:', {
          length: introText?.length || 0,
          preview: introText?.substring(0, 100) + (introText?.length > 100 ? '...' : ''),
          type: typeof introText
        });

        // Validate and sanitize intro text
        const validatedIntroText = this.validateIntroText(introText);

        // Update state to show intro API completed, main regeneration API still in progress
        this.updateIntroState({
          isLoading: false,
          text: validatedIntroText,
          isTyping: false,
          hasError: false,
          shouldReplaceText: !!targetMessageId,
          targetMessageId,
          introAPICompleted: true,
          mainAPIInProgress: true,
          showLoadingIndicator: true,
          loadingPhase: 'main'
        });

        // Start text replacement with typewriter effect and loading indicator
        if (targetMessageId) {
          this.startTextReplacementWithLoading(validatedIntroText, targetMessageId);
        } else {
          this.startTypewriterEffectWithLoading(validatedIntroText);
        }
      }),
      switchMap(introText => {
        // Now execute main regeneration API and combine results
        return mainAPICall.pipe(
          map(mainAPIResult => ({
            introText,
            mainAPIResult,
            introSuccess: true,
            mainAPISuccess: true
          })),
          tap(result => {
            this.logger.info('🎉 Main regeneration API completed successfully - updating loading state');

            // Update state to show both APIs completed
            this.updateIntroState({
              isLoading: false,
              text: this.validateIntroText(result.introText),
              isTyping: false,
              hasError: false,
              shouldReplaceText: !!targetMessageId,
              targetMessageId,
              introAPICompleted: true,
              mainAPIInProgress: false,
              showLoadingIndicator: false,
              loadingPhase: 'completed'
            });

            this.logger.info('✅ All regeneration APIs completed - loading indicators removed');
          })
        );
      }),
      catchError(error => {
        this.logger.error('❌ Regeneration API calls failed:', error);

        // Update loading state to show error
        this.updateIntroState({
          isLoading: false,
          text: 'Unable to regenerate design. Please try again.',
          isTyping: false,
          hasError: true,
          shouldReplaceText: !!targetMessageId,
          targetMessageId,
          introAPICompleted: false,
          mainAPIInProgress: false,
          showLoadingIndicator: false,
          loadingPhase: 'error',
          errorMessage: 'Regeneration API call failed'
        });

        // Handle partial failures gracefully
        return this.handleParallelAPIError(error, userRequest, mainAPICall, codeItems);
      })
    );
  }

  /**
   * Convert UI Design nodes to code items for intro API
   * @param nodes - Array of UI Design nodes
   * @returns IntroCodeItem[]
   */
  private convertNodesToCodeItems(nodes: any[]): IntroCodeItem[] {
    return nodes.map((node, index) => {
      // Extract HTML content from node data
      const htmlContent = node.data?.rawContent || node.data?.htmlContent || '';

      // Create a meaningful filename
      const fileName = node.data?.title || `design-component-${index + 1}.html`;

      return {
        fileName: fileName,
        content: typeof htmlContent === 'string' ? htmlContent : htmlContent?.toString() || ''
      };
    }).filter(item => item.content.length > 0); // Only include items with content
  }

  /**
   * Handle errors in parallel API calls with graceful degradation
   */
  private handleParallelAPIError(
    error: any,
    userRequest: string,
    mainAPICall: Observable<any>,
    codeItems?: IntroCodeItem[]
  ): Observable<ParallelAPIResult> {
    this.logger.error('🔧 Handling parallel API error gracefully');
    this.logger.error('🔧 Error details:', {
      errorType: typeof error,
      hasIntroText: 'introText' in error,
      hasMainAPIResult: 'mainAPIResult' in error,
      errorKeys: Object.keys(error || {})
    });

    // Enhanced error detection for different response formats
    const hasIntroText = error.introText !== undefined && error.introText !== null;
    const hasMainAPIResult = error.mainAPIResult !== undefined && error.mainAPIResult !== null;

    if (hasIntroText && !hasMainAPIResult) {
      // Main API failed, intro succeeded
      this.logger.info('📝 Intro API succeeded, main API failed');
      const validatedIntroText = this.validateIntroText(error.introText);
      this.startTypewriterEffect(validatedIntroText);

      return of({
        introText: validatedIntroText,
        mainAPIResult: null,
        introSuccess: true,
        mainAPISuccess: false
      });
    } else if (!hasIntroText && hasMainAPIResult) {
      // Intro API failed, main succeeded
      this.logger.info('🎯 Main API succeeded, intro API failed');
      this.showFallbackIntroMessage();

      return of({
        introText: 'Preparing your UI design...',
        mainAPIResult: error.mainAPIResult,
        introSuccess: false,
        mainAPISuccess: true
      });
    } else {
      // Both failed or unknown error structure
      this.logger.error('💥 Both APIs failed or unknown error structure');
      this.logger.error('💥 Full error object:', error);

      // Show error state but don't re-throw to prevent breaking the main flow
      this.showErrorIntroMessage();

      return of({
        introText: 'Unable to prepare design context. Please try again.',
        mainAPIResult: null,
        introSuccess: false,
        mainAPISuccess: false
      });
    }
  }

  /**
   * Start typewriter effect for intro text
   * @param text - The text to type
   */
  public startTypewriterEffect(text: string): void {
    this.logger.info('⌨️ Starting typewriter effect for intro text');

    // Clear any existing timeouts
    this.clearTypewriterTimeouts();

    // Update state to show typing (preserve existing state properties)
    const currentState = this.introMessageStateSubject.value;
    this.updateIntroState({
      isLoading: false,
      text: '',
      isTyping: true,
      hasError: false,
      shouldReplaceText: currentState.shouldReplaceText,
      targetMessageId: currentState.targetMessageId,
      introAPICompleted: currentState.introAPICompleted,
      mainAPIInProgress: currentState.mainAPIInProgress,
      showLoadingIndicator: currentState.showLoadingIndicator,
      loadingPhase: currentState.loadingPhase
    });

    // Start typing character by character
    this.typeCharacter(text, 0);
  }

  /**
   * Type a single character with delay
   * @param fullText - The complete text to type
   * @param charIndex - Current character index
   */
  private typeCharacter(fullText: string, charIndex: number): void {
    const currentState = this.introMessageStateSubject.value;

    if (charIndex >= fullText.length) {
      // Typing complete - preserve text replacement state and loading state
      this.updateIntroState({
        isLoading: false,
        text: fullText,
        isTyping: false,
        hasError: false,
        shouldReplaceText: currentState.shouldReplaceText,
        targetMessageId: currentState.targetMessageId,
        introAPICompleted: currentState.introAPICompleted,
        mainAPIInProgress: currentState.mainAPIInProgress,
        showLoadingIndicator: currentState.showLoadingIndicator,
        loadingPhase: currentState.loadingPhase
      });
      this.logger.info('✅ Typewriter effect completed');
      return;
    }

    // Update text with current progress - preserve all state
    const currentText = fullText.substring(0, charIndex + 1);
    this.updateIntroState({
      isLoading: false,
      text: currentText,
      isTyping: true,
      hasError: false,
      shouldReplaceText: currentState.shouldReplaceText,
      targetMessageId: currentState.targetMessageId,
      introAPICompleted: currentState.introAPICompleted,
      mainAPIInProgress: currentState.mainAPIInProgress,
      showLoadingIndicator: currentState.showLoadingIndicator,
      loadingPhase: currentState.loadingPhase
    });

    // Schedule next character
    const timeout = setTimeout(() => {
      this.typeCharacter(fullText, charIndex + 1);
    }, this.typingSpeed);

    this.typewriterTimeouts.push(timeout);
  }

  /**
   * Type a single character with delay while preserving loading state
   * @param fullText - The complete text to type
   * @param charIndex - Current character index
   */
  private typeCharacterWithLoading(fullText: string, charIndex: number): void {
    const currentState = this.introMessageStateSubject.value;

    if (charIndex >= fullText.length) {
      // Typing complete - preserve loading state (main API still running)
      this.updateIntroState({
        isLoading: false,
        text: fullText,
        isTyping: false,
        hasError: false,
        shouldReplaceText: currentState.shouldReplaceText,
        targetMessageId: currentState.targetMessageId,
        introAPICompleted: true,
        mainAPIInProgress: true,
        showLoadingIndicator: true,
        loadingPhase: 'main'
      });
      this.logger.info('✅ Typewriter effect with loading completed - main API still running');
      return;
    }

    // Update text with current progress - preserve loading state
    const currentText = fullText.substring(0, charIndex + 1);
    this.updateIntroState({
      isLoading: false,
      text: currentText,
      isTyping: true,
      hasError: false,
      shouldReplaceText: currentState.shouldReplaceText,
      targetMessageId: currentState.targetMessageId,
      introAPICompleted: true,
      mainAPIInProgress: true,
      showLoadingIndicator: true,
      loadingPhase: 'main'
    });

    // Schedule next character
    const timeout = setTimeout(() => {
      this.typeCharacterWithLoading(fullText, charIndex + 1);
    }, this.typingSpeed);

    this.typewriterTimeouts.push(timeout);
  }

  /**
   * Show fallback intro message when intro API fails
   */
  private showFallbackIntroMessage(): void {
    this.logger.info('🔄 Showing fallback intro message');

    // Update state to show fallback with loading indicator (main API might still be running)
    this.updateIntroState({
      isLoading: false,
      text: '',
      isTyping: true,
      hasError: false,
      shouldReplaceText: false,
      targetMessageId: undefined,
      introAPICompleted: false,
      mainAPIInProgress: true,
      showLoadingIndicator: true,
      loadingPhase: 'main'
    });

    this.startTypewriterEffectWithLoading('Preparing your UI design...');

    // Show a subtle toast notification
    this.toastService.info('Generating your design...');
  }

  /**
   * Show error intro message when APIs fail
   */
  private showErrorIntroMessage(): void {
    this.logger.error('❌ Showing error intro message');

    this.updateIntroState({
      isLoading: false,
      text: 'Unable to prepare design context. Please try again.',
      isTyping: false,
      hasError: true,
      errorMessage: 'Failed to load contextual information',
      shouldReplaceText: false,
      targetMessageId: undefined,
      introAPICompleted: false,
      mainAPIInProgress: false,
      showLoadingIndicator: false,
      loadingPhase: 'error'
    });
  }

  /**
   * Update intro message state
   * @param newState - Partial state to update
   */
  public updateIntroState(newState: Partial<IntroMessageState>): void {
    const currentState = this.introMessageStateSubject.value;
    this.introMessageStateSubject.next({ ...currentState, ...newState });
  }

  /**
   * Clear all typewriter timeouts
   */
  private clearTypewriterTimeouts(): void {
    this.typewriterTimeouts.forEach(timeout => clearTimeout(timeout));
    this.typewriterTimeouts = [];
  }

  /**
   * Start text replacement with typewriter effect for a specific message
   */
  public startTextReplacement(text: string, targetMessageId: string): void {
    this.logger.info('🔄 Starting text replacement with typewriter effect:', { text, targetMessageId });

    // Clear any existing typewriter effects
    this.clearTypewriterTimeouts();

    // Set initial state for text replacement
    this.updateIntroState({
      isLoading: false,
      text: '',
      isTyping: true,
      hasError: false,
      shouldReplaceText: true,
      targetMessageId,
      introAPICompleted: false,
      mainAPIInProgress: false,
      showLoadingIndicator: false,
      loadingPhase: 'intro'
    });

    // Start typewriter effect
    this.startTypewriterEffect(text);
  }

  /**
   * Start text replacement with typewriter effect and loading indicator for a specific message
   */
  public startTextReplacementWithLoading(text: string, targetMessageId: string): void {
    this.logger.info('🔄 Starting text replacement with typewriter effect and loading indicator:', { text, targetMessageId });

    // Clear any existing typewriter effects
    this.clearTypewriterTimeouts();

    // Set initial state for text replacement with loading
    this.updateIntroState({
      isLoading: false,
      text: '',
      isTyping: true,
      hasError: false,
      shouldReplaceText: true,
      targetMessageId,
      introAPICompleted: true,
      mainAPIInProgress: true,
      showLoadingIndicator: true,
      loadingPhase: 'main'
    });

    // Start typewriter effect with loading state preserved
    this.startTypewriterEffectWithLoading(text);
  }

  /**
   * Start typewriter effect with loading indicator
   */
  public startTypewriterEffectWithLoading(text: string): void {
    this.logger.info('⌨️ Starting typewriter effect with loading indicator for intro text');

    // Clear any existing timeouts
    this.clearTypewriterTimeouts();

    // Update state to show typing with loading indicator (preserve loading state)
    const currentState = this.introMessageStateSubject.value;
    this.updateIntroState({
      isLoading: false,
      text: '',
      isTyping: true,
      hasError: false,
      shouldReplaceText: currentState.shouldReplaceText,
      targetMessageId: currentState.targetMessageId,
      introAPICompleted: true,
      mainAPIInProgress: true,
      showLoadingIndicator: true,
      loadingPhase: 'main'
    });

    // Start typing character by character with loading state preserved
    this.typeCharacterWithLoading(text, 0);
  }

  /**
   * Complete text replacement and clear all loading states
   */
  public completeTextReplacement(): void {
    this.logger.info('✅ Completing text replacement and clearing all loading states');

    const currentState = this.introMessageStateSubject.getValue();
    this.updateIntroState({
      ...currentState,
      isLoading: false,
      isTyping: false,
      shouldReplaceText: false,
      targetMessageId: undefined,
      introAPICompleted: true,
      mainAPIInProgress: false,
      showLoadingIndicator: false,
      loadingPhase: 'completed'
    });
  }

  /**
   * Reset intro message state and all loading indicators
   */
  public resetIntroState(): void {
    this.logger.info('🧹 Resetting intro message state and all loading indicators');
    this.clearTypewriterTimeouts();

    this.updateIntroState({
      isLoading: false,
      text: '',
      isTyping: false,
      hasError: false,
      errorMessage: undefined,
      shouldReplaceText: false,
      targetMessageId: undefined,
      introAPICompleted: false,
      mainAPIInProgress: false,
      showLoadingIndicator: false,
      loadingPhase: 'intro',
      messageType: 'generation'
    });
  }

  /**
   * Validate and sanitize intro text from API response
   * @param introText - Raw intro text from API
   * @returns Validated and cleaned intro text
   */
  private validateIntroText(introText: string): string {
    // Handle null, undefined, or non-string values
    if (!introText || typeof introText !== 'string') {
      this.logger.warn('🎭 Invalid intro text received:', typeof introText);
      return 'Preparing your UI design...';
    }

    // Clean and validate the text
    const cleanedText = introText.trim();

    if (cleanedText.length === 0) {
      this.logger.warn('🎭 Empty intro text after trimming');
      return 'Preparing your UI design...';
    }

    // Check for reasonable length limits
    if (cleanedText.length > 1000) {
      this.logger.warn('🎭 Intro text is very long, truncating:', cleanedText.length);
      return cleanedText.substring(0, 997) + '...';
    }

    // Check for minimum meaningful length
    if (cleanedText.length < 10) {
      this.logger.warn('🎭 Intro text is very short, using fallback:', cleanedText);
      return 'Preparing your UI design...';
    }

    this.logger.info('🎭 Intro text validated successfully:', {
      originalLength: introText.length,
      cleanedLength: cleanedText.length,
      preview: cleanedText.substring(0, 50) + (cleanedText.length > 50 ? '...' : '')
    });

    return cleanedText;
  }

  /**
   * Get current intro message state
   */
  public getCurrentIntroState(): IntroMessageState {
    return this.introMessageStateSubject.value;
  }

  /**
   * Cleanup on service destruction
   */
  ngOnDestroy(): void {
    this.clearTypewriterTimeouts();
  }
}
